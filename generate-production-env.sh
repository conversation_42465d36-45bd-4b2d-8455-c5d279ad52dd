#!/bin/bash

# Langfuse 生产环境配置生成脚本
# Generate production environment configuration for Langfuse

set -e

echo "🚀 Langfuse 生产环境配置生成器"
echo "=================================="

# 检查是否存在 .env 文件
if [ ! -f ".env" ]; then
    echo "❌ 错误：找不到 .env 开发环境配置文件"
    echo "请确保在 Langfuse 项目根目录运行此脚本"
    exit 1
fi

# 生成安全密钥
echo "🔐 生成安全密钥..."
NEXTAUTH_SECRET=$(openssl rand -base64 32)
ENCRYPTION_KEY=$(openssl rand -hex 32)
POSTGRES_PASSWORD=$(openssl rand -base64 24 | tr -d "=+/" | cut -c1-20)
CLICKHOUSE_PASSWORD=$(openssl rand -base64 24 | tr -d "=+/" | cut -c1-20)
REDIS_PASSWORD=$(openssl rand -base64 24 | tr -d "=+/" | cut -c1-20)
MINIO_PASSWORD=$(openssl rand -base64 24 | tr -d "=+/" | cut -c1-20)
ADMIN_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)

# 获取用户输入
echo ""
echo "📝 请输入部署信息："
read -p "服务器域名或IP (例: https://langfuse.yourdomain.com 或 http://*************:3000): " NEXTAUTH_URL
read -p "管理员邮箱 (默认: <EMAIL>): " ADMIN_EMAIL
ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}

# 复制并修改配置文件
echo ""
echo "📄 生成生产环境配置文件..."
cp .env.production.new .env.production.generated

# 替换配置值
sed -i.bak "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=${NEXTAUTH_URL}|g" .env.production.generated
sed -i.bak "s|NEXTAUTH_SECRET=.*|NEXTAUTH_SECRET=${NEXTAUTH_SECRET}|g" .env.production.generated
sed -i.bak "s|ENCRYPTION_KEY=.*|ENCRYPTION_KEY=${ENCRYPTION_KEY}|g" .env.production.generated
sed -i.bak "s|POSTGRES_PASSWORD=.*|POSTGRES_PASSWORD=${POSTGRES_PASSWORD}|g" .env.production.generated
sed -i.bak "s|CLICKHOUSE_PASSWORD=.*|CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD}|g" .env.production.generated
sed -i.bak "s|REDIS_AUTH=.*|REDIS_AUTH=${REDIS_PASSWORD}|g" .env.production.generated
sed -i.bak "s|MINIO_ROOT_PASSWORD=.*|MINIO_ROOT_PASSWORD=${MINIO_PASSWORD}|g" .env.production.generated
sed -i.bak "s|LANGFUSE_S3_EVENT_UPLOAD_SECRET_ACCESS_KEY=.*|LANGFUSE_S3_EVENT_UPLOAD_SECRET_ACCESS_KEY=${MINIO_PASSWORD}|g" .env.production.generated
sed -i.bak "s|LANGFUSE_S3_MEDIA_UPLOAD_SECRET_ACCESS_KEY=.*|LANGFUSE_S3_MEDIA_UPLOAD_SECRET_ACCESS_KEY=${MINIO_PASSWORD}|g" .env.production.generated
sed -i.bak "s|LANGFUSE_S3_BATCH_EXPORT_SECRET_ACCESS_KEY=.*|LANGFUSE_S3_BATCH_EXPORT_SECRET_ACCESS_KEY=${MINIO_PASSWORD}|g" .env.production.generated
sed -i.bak "s|LANGFUSE_INIT_USER_EMAIL=.*|LANGFUSE_INIT_USER_EMAIL=${ADMIN_EMAIL}|g" .env.production.generated
sed -i.bak "s|LANGFUSE_INIT_USER_PASSWORD=.*|LANGFUSE_INIT_USER_PASSWORD=${ADMIN_PASSWORD}|g" .env.production.generated

# 更新服务器相关配置
if [[ $NEXTAUTH_URL == *"://"* ]]; then
    SERVER_IP=$(echo $NEXTAUTH_URL | sed 's|.*://||' | sed 's|:.*||')
    sed -i.bak "s|your-server-ip|${SERVER_IP}|g" .env.production.generated
    sed -i.bak "s|your-domain.com|${SERVER_IP}|g" .env.production.generated
fi

# 清理备份文件
rm -f .env.production.generated.bak

echo ""
echo "✅ 生产环境配置文件已生成: .env.production.generated"
echo ""
echo "🔑 生成的凭据信息："
echo "=================================="
echo "管理员邮箱: ${ADMIN_EMAIL}"
echo "管理员密码: ${ADMIN_PASSWORD}"
echo "PostgreSQL 密码: ${POSTGRES_PASSWORD}"
echo "ClickHouse 密码: ${CLICKHOUSE_PASSWORD}"
echo "Redis 密码: ${REDIS_PASSWORD}"
echo "MinIO 密码: ${MINIO_PASSWORD}"
echo ""
echo "⚠️  请妥善保存这些凭据！"
echo ""
echo "📋 下一步操作："
echo "1. 检查并调整 .env.production.generated 文件"
echo "2. 重命名为 .env.production: mv .env.production.generated .env.production"
echo "3. 确保 ClickHouse 配置存在: mkdir -p config/clickhouse"
echo "4. 创建 ClickHouse 网络配置文件"
echo "5. 启动服务: docker compose -f docker-compose.prod.yml --env-file .env.production up -d"
