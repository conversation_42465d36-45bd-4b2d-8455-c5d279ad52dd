#!/bin/bash

# Langfuse 生产环境一键部署脚本
# One-click production deployment script for Langfuse

set -e

echo "🚀 Langfuse 生产环境一键部署"
echo "============================="

# 检查必需的工具
check_requirements() {
    echo "🔍 检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker compose &> /dev/null; then
        echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! command -v openssl &> /dev/null; then
        echo "❌ OpenSSL 未安装，请先安装 OpenSSL"
        exit 1
    fi
    
    echo "✅ 系统要求检查通过"
}

# 创建 ClickHouse 配置
create_clickhouse_config() {
    echo "📁 创建 ClickHouse 配置..."
    
    mkdir -p config/clickhouse
    
    cat > config/clickhouse/listen.xml << 'EOF'
<?xml version="1.0"?>
<clickhouse>
    <!-- Listen on all interfaces to allow connections from other containers -->
    <listen_host>0.0.0.0</listen_host>
    <listen_host>::</listen_host>
</clickhouse>
EOF
    
    echo "✅ ClickHouse 配置已创建"
}

# 生成环境配置
generate_env_config() {
    echo "📝 生成环境配置..."
    
    if [ ! -f ".env.production.new" ]; then
        echo "❌ 找不到 .env.production.new 模板文件"
        exit 1
    fi
    
    # 运行配置生成脚本
    if [ -f "generate-production-env.sh" ]; then
        ./generate-production-env.sh
        if [ -f ".env.production.generated" ]; then
            mv .env.production.generated .env.production
            echo "✅ 生产环境配置已生成"
        fi
    else
        echo "⚠️  请手动配置 .env.production 文件"
        cp .env.production.new .env.production
        echo "📄 已复制模板文件，请编辑 .env.production 并修改所有 CHANGEME 项"
        read -p "配置完成后按回车继续..."
    fi
}

# 启动服务
start_services() {
    echo "🐳 启动 Docker 服务..."
    
    # 停止可能存在的服务
    docker compose -f docker-compose.prod.yml --env-file .env.production down 2>/dev/null || true
    
    # 启动服务
    docker compose -f docker-compose.prod.yml --env-file .env.production up -d
    
    echo "✅ 服务启动完成"
}

# 检查服务状态
check_services() {
    echo "🔍 检查服务状态..."
    
    sleep 10
    
    echo "📊 服务状态："
    docker compose -f docker-compose.prod.yml --env-file .env.production ps
    
    echo ""
    echo "📋 健康检查："
    
    # 检查 PostgreSQL
    if docker compose -f docker-compose.prod.yml --env-file .env.production exec -T postgres pg_isready -U langfuse &>/dev/null; then
        echo "✅ PostgreSQL: 健康"
    else
        echo "❌ PostgreSQL: 不健康"
    fi
    
    # 检查 ClickHouse
    if curl -s http://localhost:8123/ping &>/dev/null; then
        echo "✅ ClickHouse: 健康"
    else
        echo "❌ ClickHouse: 不健康"
    fi
    
    # 检查 Redis
    if docker compose -f docker-compose.prod.yml --env-file .env.production exec -T redis redis-cli ping &>/dev/null; then
        echo "✅ Redis: 健康"
    else
        echo "❌ Redis: 不健康"
    fi
    
    # 检查 Web 应用
    if curl -s http://localhost:3000 &>/dev/null; then
        echo "✅ Web 应用: 健康"
    else
        echo "⏳ Web 应用: 启动中..."
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "🎉 部署完成！"
    echo "=============="
    
    # 从环境文件读取配置
    if [ -f ".env.production" ]; then
        NEXTAUTH_URL=$(grep "NEXTAUTH_URL=" .env.production | cut -d'=' -f2)
        ADMIN_EMAIL=$(grep "LANGFUSE_INIT_USER_EMAIL=" .env.production | cut -d'=' -f2)
        
        echo "🌐 访问地址: ${NEXTAUTH_URL}"
        echo "👤 管理员邮箱: ${ADMIN_EMAIL}"
        echo "🔑 管理员密码: 请查看部署日志或 .env.production 文件"
        echo ""
        echo "📊 管理面板:"
        echo "   - MinIO: http://localhost:9001"
        echo "   - ClickHouse: http://localhost:8123/play"
        echo ""
        echo "🔧 管理命令:"
        echo "   查看日志: docker compose -f docker-compose.prod.yml --env-file .env.production logs -f"
        echo "   停止服务: docker compose -f docker-compose.prod.yml --env-file .env.production down"
        echo "   重启服务: docker compose -f docker-compose.prod.yml --env-file .env.production restart"
    fi
}

# 主函数
main() {
    check_requirements
    create_clickhouse_config
    generate_env_config
    start_services
    check_services
    show_deployment_info
}

# 运行主函数
main "$@"
